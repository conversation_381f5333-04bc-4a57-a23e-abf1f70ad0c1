@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .return-status-display {
    @apply flex items-center gap-2 mb-2;
  }

  .status-badge {
    @apply text-white px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wide;
  }

  .edit-status-btn {
    @apply bg-transparent border-0 cursor-pointer p-1 rounded transition-colors duration-200 text-sm hover:bg-gray-100;
  }

  .return-status-manager {
    @apply relative bg-white border border-gray-200 rounded-2xl shadow-lg p-4 mb-4 z-10;
  }

  .status-form {
    @apply flex flex-col gap-4;
  }

  .form-header {
    @apply flex justify-between items-center mb-2;
  }

  .form-header h4 {
    @apply m-0 text-base font-semibold text-gray-900;
  }

  .close-btn {
    @apply bg-transparent border-0 cursor-pointer text-xl text-gray-500 p-0 w-6 h-6 flex items-center justify-center rounded transition-colors duration-200 hover:text-gray-700 hover:bg-gray-100;
  }

  .error-message {
    @apply bg-red-50 border border-red-200 text-red-800 p-3 rounded-md text-sm;
  }

  .form-group {
    @apply flex flex-col gap-2;
  }

  .form-group label {
    @apply font-medium text-gray-700 text-sm;
  }

  .form-group select,
  .form-group textarea {
    @apply px-3 py-2 border border-gray-300 rounded-md text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  }

  .form-group textarea {
    @apply resize-y min-h-[80px];
  }

  .form-group select:disabled,
  .form-group textarea:disabled {
    @apply bg-gray-50 text-gray-600 cursor-not-allowed;
  }

  .form-actions {
    @apply flex gap-3 justify-end mt-2;
  }

  .btn-cancel,
  .btn-save {
    @apply px-4 py-2 rounded-md font-medium cursor-pointer transition-all duration-200 border-0 text-sm;
  }

  .btn-cancel {
    @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50;
  }

  .btn-save {
    @apply bg-blue-600 text-white hover:bg-blue-700;
  }

  .btn-cancel:disabled,
  .btn-save:disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  /* Responsive design */
  @media (max-width: 640px) {
    .return-status-manager {
      @apply p-3;
    }

    .form-actions {
      @apply flex-col;
    }

    .btn-cancel,
    .btn-save {
      @apply w-full;
    }
  }
}
